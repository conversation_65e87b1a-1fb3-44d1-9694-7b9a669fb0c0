-- Add composite unique constraint for bank_name and account_number combination
-- This ensures that the same account number can exist across different banks
-- but not within the same bank

-- First, remove the existing unique constraint on account_number only
ALTER TABLE bank_accounts DROP CONSTRAINT IF EXISTS bank_accounts_account_number_key;

-- Add the new composite unique constraint
ALTER TABLE bank_accounts ADD CONSTRAINT uk_bank_accounts_bank_name_account_number 
    UNIQUE (bank_name, account_number);

-- Add comment for documentation
COMMENT ON CONSTRAINT uk_bank_accounts_bank_name_account_number ON bank_accounts 
    IS 'Ensures uniqueness of account number within each bank, allowing same account numbers across different banks';
